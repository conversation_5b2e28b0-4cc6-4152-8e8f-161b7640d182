# generated by rye
# use `rye lock` or `rye sync` to update this lockfile
#
# last locked with the following flags:
#   pre: false
#   features: []
#   all-features: false
#   with-sources: false
#   generate-hashes: false
#   universal: false

-e file:.
aiohappyeyeballs==2.6.1
    # via aiohttp
aiohttp==3.12.15
    # via discord-py
    # via litellm
aiosignal==1.4.0
    # via aiohttp
annotated-types==0.7.0
    # via pydantic
anyio==4.10.0
    # via httpx
    # via openai
arazzo-runner==0.8.22
    # via jentic
attrs==25.3.0
    # via aiohttp
    # via jsonschema
    # via referencing
blinker==1.9.0
    # via flask
braceexpand==0.1.7
    # via jishaku
cachetools==5.5.2
    # via google-auth
certifi==2025.8.3
    # via httpcore
    # via httpx
    # via requests
charset-normalizer==3.4.3
    # via requests
click==8.2.1
    # via flask
    # via jishaku
    # via litellm
discord==2.3.2
    # via jentic-summer-hackathon
discord-ext-bot==1.0.1
    # via jentic-summer-hackathon
discord-py==2.5.2
    # via discord
    # via discord-ext-bot
    # via jentic-summer-hackathon
    # via jishaku
distro==1.9.0
    # via openai
filelock==3.19.1
    # via huggingface-hub
flask==3.1.1
    # via jentic-summer-hackathon
frozenlist==1.7.0
    # via aiohttp
    # via aiosignal
fsspec==2025.7.0
    # via huggingface-hub
google-ai-generativelanguage==0.6.15
    # via google-generativeai
google-api-core==2.25.1
    # via google-ai-generativelanguage
    # via google-api-python-client
    # via google-generativeai
google-api-python-client==2.179.0
    # via google-generativeai
google-auth==2.40.3
    # via google-ai-generativelanguage
    # via google-api-core
    # via google-api-python-client
    # via google-auth-httplib2
    # via google-generativeai
google-auth-httplib2==0.2.0
    # via google-api-python-client
google-generativeai==0.8.5
    # via jentic-summer-hackathon
googleapis-common-protos==1.70.0
    # via google-api-core
    # via grpcio-status
grpcio==1.74.0
    # via google-api-core
    # via grpcio-status
grpcio-status==1.71.2
    # via google-api-core
h11==0.16.0
    # via httpcore
hf-xet==1.1.7
    # via huggingface-hub
httpcore==1.0.9
    # via httpx
httplib2==0.22.0
    # via google-api-python-client
    # via google-auth-httplib2
httpx==0.28.1
    # via jentic
    # via litellm
    # via openai
huggingface-hub==0.34.4
    # via tokenizers
idna==3.10
    # via anyio
    # via httpx
    # via requests
    # via yarl
import-expression==2.2.1.post1
    # via jishaku
importlib-metadata==8.7.0
    # via litellm
iniconfig==2.1.0
    # via pytest
itsdangerous==2.2.0
    # via flask
jentic==0.9.4
    # via jentic-summer-hackathon
jinja2==3.1.6
    # via flask
    # via litellm
jishaku==2.6.0
    # via discord-ext-bot
jiter==0.10.0
    # via openai
jsonpath-ng==1.7.0
    # via arazzo-runner
jsonpointer==3.0.0
    # via arazzo-runner
jsonschema==4.25.0
    # via litellm
jsonschema-specifications==2025.4.1
    # via jsonschema
litellm==1.75.7
    # via jentic-summer-hackathon
markupsafe==3.0.2
    # via flask
    # via jinja2
    # via werkzeug
multidict==6.6.4
    # via aiohttp
    # via yarl
openai==1.99.9
    # via jentic-summer-hackathon
    # via litellm
packaging==25.0
    # via huggingface-hub
    # via pytest
pluggy==1.6.0
    # via pytest
ply==3.11
    # via jsonpath-ng
propcache==0.3.2
    # via aiohttp
    # via yarl
proto-plus==1.26.1
    # via google-ai-generativelanguage
    # via google-api-core
protobuf==5.29.5
    # via google-ai-generativelanguage
    # via google-api-core
    # via google-generativeai
    # via googleapis-common-protos
    # via grpcio-status
    # via proto-plus
pyasn1==0.6.1
    # via pyasn1-modules
    # via rsa
pyasn1-modules==0.4.2
    # via google-auth
pydantic==2.11.7
    # via arazzo-runner
    # via google-generativeai
    # via jentic
    # via jentic-summer-hackathon
    # via litellm
    # via openai
    # via pydantic-settings
pydantic-core==2.33.2
    # via pydantic
pydantic-settings==2.10.1
    # via jentic-summer-hackathon
pygments==2.19.2
    # via pytest
pyparsing==3.2.3
    # via httplib2
pytest==8.4.1
    # via jentic-summer-hackathon
    # via pytest-asyncio
pytest-asyncio==1.1.0
    # via jentic-summer-hackathon
python-dotenv==1.1.1
    # via jentic-summer-hackathon
    # via litellm
    # via pydantic-settings
pyyaml==6.0.2
    # via arazzo-runner
    # via huggingface-hub
    # via jentic-summer-hackathon
referencing==0.36.2
    # via jsonschema
    # via jsonschema-specifications
regex==2025.7.34
    # via tiktoken
requests==2.32.4
    # via arazzo-runner
    # via google-api-core
    # via huggingface-hub
    # via tiktoken
rpds-py==0.27.0
    # via jsonschema
    # via referencing
rsa==4.9.1
    # via google-auth
sniffio==1.3.1
    # via anyio
    # via openai
structlog==25.4.0
    # via jentic-summer-hackathon
tabulate==0.9.0
    # via jishaku
tenacity==9.1.2
    # via jentic
tiktoken==0.11.0
    # via litellm
tokenizers==0.21.4
    # via litellm
tqdm==4.67.1
    # via google-generativeai
    # via huggingface-hub
    # via openai
typing-extensions==4.14.1
    # via aiosignal
    # via anyio
    # via google-generativeai
    # via huggingface-hub
    # via jishaku
    # via openai
    # via pydantic
    # via pydantic-core
    # via referencing
    # via typing-inspection
typing-inspection==0.4.1
    # via pydantic
    # via pydantic-settings
uritemplate==4.2.0
    # via google-api-python-client
urllib3==2.5.0
    # via requests
werkzeug==3.1.3
    # via flask
yarl==1.20.1
    # via aiohttp
zipp==3.23.0
    # via importlib-metadata
