{"log": {"version": "1.2", "creator": {"name": "Chrome DevTools", "version": "120.0.0.0"}, "entries": [{"startedDateTime": "2025-08-16T11:30:00.000Z", "time": 245, "request": {"method": "GET", "url": "https://jsonplaceholder.typicode.com/posts", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/plain, */*"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://jsonplaceholder.typicode.com/"}], "queryString": [], "cookies": [], "headersSize": 234, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "Cache-Control", "value": "max-age=43200"}], "content": {"size": 6395, "mimeType": "application/json", "text": "[{\"userId\":1,\"id\":1,\"title\":\"sunt aut facere repellat provident occaecati excepturi optio reprehenderit\",\"body\":\"quia et suscipit\\nsuscipit recusandae consequuntur expedita et cum\\nreprehenderit molestiae ut ut quas totam\\nnostrum rerum est autem sunt rem eveniet architecto\"},{\"userId\":1,\"id\":2,\"title\":\"qui est esse\",\"body\":\"est rerum tempore vitae\\nsequi sint nihil reprehenderit dolor beatae ea dolores neque\\nfugiat blanditiis voluptate porro vel nihil molestiae ut reiciendis\\nqui aperiam non debitis possimus qui neque nisi nulla\"},{\"userId\":1,\"id\":3,\"title\":\"ea molestias quasi exercitationem repellat qui ipsa sit aut\",\"body\":\"et iusto sed quo iure\\nvoluptatem occaecati omnis eligendi aut ad\\nvoluptatem doloribus vel accusantium quis pariatur\\nmolestiae porro eius odio et labore et velit aut\"}]"}, "redirectURL": "", "headersSize": 234, "bodySize": 6395}, "cache": {}, "timings": {"blocked": 1, "dns": 2, "connect": 15, "send": 1, "wait": 220, "receive": 6}}, {"startedDateTime": "2025-08-16T11:30:05.000Z", "time": 180, "request": {"method": "GET", "url": "https://jsonplaceholder.typicode.com/posts/1", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/plain, */*"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"}], "queryString": [], "cookies": [], "headersSize": 234, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Access-Control-Allow-Origin", "value": "*"}], "content": {"size": 292, "mimeType": "application/json", "text": "{\"userId\":1,\"id\":1,\"title\":\"sunt aut facere repellat provident occaecati excepturi optio reprehenderit\",\"body\":\"quia et suscipit\\nsuscipit recusandae consequuntur expedita et cum\\nreprehenderit molestiae ut ut quas totam\\nnostrum rerum est autem sunt rem eveniet architecto\"}"}, "redirectURL": "", "headersSize": 234, "bodySize": 292}, "cache": {}, "timings": {"blocked": 1, "dns": 0, "connect": 0, "send": 1, "wait": 175, "receive": 3}}, {"startedDateTime": "2025-08-16T11:30:10.000Z", "time": 320, "request": {"method": "POST", "url": "https://jsonplaceholder.typicode.com/posts", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Type", "value": "application/json; charset=UTF-8"}, {"name": "Accept", "value": "application/json, text/plain, */*"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"}], "queryString": [], "cookies": [], "headersSize": 234, "bodySize": 76, "postData": {"mimeType": "application/json", "text": "{\"title\":\"foo\",\"body\":\"bar\",\"userId\":1}"}}, "response": {"status": 201, "statusText": "Created", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Access-Control-Allow-Origin", "value": "*"}], "content": {"size": 64, "mimeType": "application/json", "text": "{\"title\":\"foo\",\"body\":\"bar\",\"userId\":1,\"id\":101}"}, "redirectURL": "", "headersSize": 234, "bodySize": 64}, "cache": {}, "timings": {"blocked": 1, "dns": 0, "connect": 0, "send": 2, "wait": 315, "receive": 2}}, {"startedDateTime": "2025-08-16T11:30:15.000Z", "time": 280, "request": {"method": "PUT", "url": "https://jsonplaceholder.typicode.com/posts/1", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Type", "value": "application/json; charset=UTF-8"}, {"name": "Accept", "value": "application/json, text/plain, */*"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"}], "queryString": [], "cookies": [], "headersSize": 234, "bodySize": 82, "postData": {"mimeType": "application/json", "text": "{\"id\":1,\"title\":\"foo\",\"body\":\"bar\",\"userId\":1}"}}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Access-Control-Allow-Origin", "value": "*"}], "content": {"size": 58, "mimeType": "application/json", "text": "{\"id\":1,\"title\":\"foo\",\"body\":\"bar\",\"userId\":1}"}, "redirectURL": "", "headersSize": 234, "bodySize": 58}, "cache": {}, "timings": {"blocked": 1, "dns": 0, "connect": 0, "send": 2, "wait": 275, "receive": 2}}, {"startedDateTime": "2025-08-16T11:30:20.000Z", "time": 150, "request": {"method": "DELETE", "url": "https://jsonplaceholder.typicode.com/posts/1", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/plain, */*"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"}], "queryString": [], "cookies": [], "headersSize": 234, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Access-Control-Allow-Origin", "value": "*"}], "content": {"size": 2, "mimeType": "application/json", "text": "{}"}, "redirectURL": "", "headersSize": 234, "bodySize": 2}, "cache": {}, "timings": {"blocked": 1, "dns": 0, "connect": 0, "send": 1, "wait": 145, "receive": 3}}, {"startedDateTime": "2025-08-16T11:30:25.000Z", "time": 200, "request": {"method": "GET", "url": "https://jsonplaceholder.typicode.com/users", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/plain, */*"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"}], "queryString": [], "cookies": [], "headersSize": 234, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Access-Control-Allow-Origin", "value": "*"}], "content": {"size": 1234, "mimeType": "application/json", "text": "[{\"id\":1,\"name\":\"<PERSON><PERSON>\",\"username\":\"<PERSON><PERSON>\",\"email\":\"<EMAIL>\",\"address\":{\"street\":\"<PERSON>las Light\",\"suite\":\"Apt. 556\",\"city\":\"Gwenborough\",\"zipcode\":\"92998-3874\",\"geo\":{\"lat\":\"-37.3159\",\"lng\":\"81.1496\"}},\"phone\":\"************** x56442\",\"website\":\"hildegard.org\",\"company\":{\"name\":\"Romaguera-Crona\",\"catchPhrase\":\"Multi-layered client-server neural-net\",\"bs\":\"harness real-time e-markets\"}}]"}, "redirectURL": "", "headersSize": 234, "bodySize": 1234}, "cache": {}, "timings": {"blocked": 1, "dns": 0, "connect": 0, "send": 1, "wait": 195, "receive": 3}}]}}