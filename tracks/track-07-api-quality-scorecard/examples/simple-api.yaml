openapi: 3.0.0
info:
  title: Simple Example API
  description: A basic API for testing the quality scorecard
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>

servers:
  - url: https://api.example.com/v1
    description: Production server

paths:
  /users:
    get:
      operationId: getUsers
      summary: List all users
      description: Retrieve a paginated list of all users in the system
      tags:
        - Users
      parameters:
        - name: limit
          in: query
          description: Maximum number of users to return
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: offset
          in: query
          description: Number of users to skip for pagination
          required: false
          schema:
            type: integer
            minimum: 0
            default: 0
      responses:
        '200':
          description: List of users retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  users:
                    type: array
                    items:
                      $ref: '#/components/schemas/User'
                  total:
                    type: integer
                    description: Total number of users
                  limit:
                    type: integer
                  offset:
                    type: integer
              example:
                users:
                  - id: 1
                    name: "<PERSON>"
                    email: "<EMAIL>"
                    created_at: "2023-01-01T00:00:00Z"
                total: 150
                limit: 20
                offset: 0
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    
    post:
      operationId: createUser
      summary: Create a new user
      description: Create a new user account with the provided information
      tags:
        - Users
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserRequest'
            example:
              name: "Jane Smith"
              email: "<EMAIL>"
      responses:
        '201':
          description: User created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
              example:
                id: 2
                name: "Jane Smith"
                email: "<EMAIL>"
                created_at: "2023-01-02T12:00:00Z"
        '400':
          description: Invalid user data provided
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '409':
          description: User with this email already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /users/{userId}:
    get:
      operationId: getUserById
      summary: Get user by ID
      description: Retrieve detailed information about a specific user
      tags:
        - Users
      parameters:
        - name: userId
          in: path
          required: true
          description: Unique identifier for the user
          schema:
            type: integer
            minimum: 1
      responses:
        '200':
          description: User details retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
              example:
                id: 1
                name: "John Doe"
                email: "<EMAIL>"
                created_at: "2023-01-01T00:00:00Z"
        '404':
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

components:
  schemas:
    User:
      type: object
      required:
        - id
        - name
        - email
        - created_at
      properties:
        id:
          type: integer
          description: Unique identifier for the user
          example: 1
        name:
          type: string
          description: Full name of the user
          minLength: 1
          maxLength: 100
          example: "John Doe"
        email:
          type: string
          format: email
          description: Email address of the user
          example: "<EMAIL>"
        created_at:
          type: string
          format: date-time
          description: When the user account was created
          example: "2023-01-01T00:00:00Z"
    
    CreateUserRequest:
      type: object
      required:
        - name
        - email
      properties:
        name:
          type: string
          description: Full name of the user
          minLength: 1
          maxLength: 100
          example: "Jane Smith"
        email:
          type: string
          format: email
          description: Email address of the user
          example: "<EMAIL>"
    
    Error:
      type: object
      required:
        - error
        - message
      properties:
        error:
          type: string
          description: Error code identifier
          example: "INVALID_REQUEST"
        message:
          type: string
          description: Human-readable error message
          example: "The provided email address is not valid"
        details:
          type: object
          description: Additional error details
          additionalProperties: true

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token-based authentication

security:
  - BearerAuth: []

tags:
  - name: Users
    description: User management operations