# Core dependencies for OpenAPI processing
pyyaml>=6.0.0
jsonschema>=4.0.0
openapi-spec-validator>=0.7.0

# Data handling and analysis
pydantic>=2.0.0
pydantic-settings>=2.0.0
python-dotenv>=1.0.0

# Report generation
jinja2>=3.1.0  # For HTML report templates
markdown>=3.4.0  # For markdown report generation
plotly>=5.0.0  # For charts and visualizations (optional)

# CLI and utilities
click>=8.0.0  # For command-line interface
rich>=13.0.0  # For pretty console output
tabulate>=0.9.0  # For table formatting

# Testing and validation
pytest>=7.0.0
pytest-cov>=4.0.0
requests>=2.28.0  # For fetching remote specs

# Optional: Advanced analysis
textstat>=0.7.0  # For text readability analysis
nltk>=3.8.0  # For natural language processing (optional)