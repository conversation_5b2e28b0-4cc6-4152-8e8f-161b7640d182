<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Quality Scorecard - Simple Example API</title>
    <style>
        
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .api-info h2 {
            color: #34495e;
            margin-bottom: 5px;
        }
        
        .score-summary {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            color: white;
        }
        
        .overall-score {
            text-align: center;
        }
        
        .score-value {
            font-size: 3em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .score-label {
            font-size: 1.2em;
            margin-bottom: 5px;
        }
        
        .grade {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .quick-stats {
            display: flex;
            gap: 30px;
        }
        
        .stat {
            text-align: center;
        }
        
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        
        .category-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .category-card {
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .category-card.success { background: #d4edda; border-left: 4px solid #28a745; }
        .category-card.warning { background: #fff3cd; border-left: 4px solid #ffc107; }
        .category-card.danger { background: #f8d7da; border-left: 4px solid #dc3545; }
        
        .category-name {
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .category-score {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .category-percentage {
            color: #666;
            margin-bottom: 10px;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #eee;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 0.3s ease;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .metric {
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 3px solid #007bff;
        }
        
        .metric-name {
            font-weight: 500;
            margin-bottom: 5px;
        }
        
        .metric-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #007bff;
        }
        
        .recommendations-summary {
            margin-bottom: 30px;
        }
        
        .recommendations-summary h3 {
            margin-bottom: 20px;
            color: #2c3e50;
        }
        
        .critical-issues, .high-issues {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 6px;
        }
        
        .critical-issues {
            background: #f8d7da;
            border-left: 4px solid #dc3545;
        }
        
        .high-issues {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
        }
        
        .no-issues {
            padding: 20px;
            text-align: center;
            background: #d4edda;
            border-radius: 6px;
            color: #155724;
            font-weight: 500;
        }
        
        .recommendation {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #ccc;
        }
        
        .recommendation.danger { border-left-color: #dc3545; background: #f8d7da; }
        .recommendation.warning { border-left-color: #ffc107; background: #fff3cd; }
        .recommendation.info { border-left-color: #17a2b8; background: #d1ecf1; }
        
        .rec-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .rec-title {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .rec-severity {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .rec-severity.critical { background: #dc3545; color: white; }
        .rec-severity.high { background: #fd7e14; color: white; }
        .rec-severity.medium { background: #ffc107; color: #212529; }
        .rec-severity.low { background: #17a2b8; color: white; }
        
        .rec-description {
            margin-bottom: 10px;
            color: #495057;
        }
        
        .rec-operation, .rec-parameter {
            margin-bottom: 5px;
            font-size: 0.9em;
            color: #6c757d;
        }
        
        .rec-fix {
            margin-bottom: 10px;
            padding: 10px;
            background: rgba(0,0,0,0.05);
            border-radius: 4px;
            font-size: 0.9em;
        }
        
        .rec-impact {
            font-size: 0.8em;
            color: #6c757d;
            text-align: right;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #6c757d;
        }
        
        code {
            background: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 0.9em;
        }
        
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>API Quality Scorecard</h1>
            <div class="api-info">
                <h2>Simple Example API</h2>
                <p>Version: 1.0.0 | 
                   OpenAPI: 3.0 | 
                   Analyzed: 2025-08-16 15:37:52 UTC</p>
            </div>
        </header>

        <div class="score-summary">
            <div class="overall-score success">
                <div class="score-value">89</div>
                <div class="score-label">Overall Score</div>
                <div class="grade">Grade: B</div>
            </div>
            
            <div class="quick-stats">
                <div class="stat">
                    <div class="stat-value">3</div>
                    <div class="stat-label">Operations</div>
                </div>
                <div class="stat critical">
                    <div class="stat-value">0</div>
                    <div class="stat-label">Critical Issues</div>
                </div>
                <div class="stat high">
                    <div class="stat-value">0</div>
                    <div class="stat-label">High Priority</div>
                </div>
            </div>
        </div>

        <div class="categories">
            <h3>Category Scores</h3>
            <div class="category-grid">
                
        <div class="category-card success">
            <div class="category-name">Documentation</div>
            <div class="category-score">23/25</div>
            <div class="category-percentage">92.0%</div>
            <div class="progress-bar">
                <div class="progress-fill" style="width: 92.0%"></div>
            </div>
        </div>
                
        <div class="category-card success">
            <div class="category-name">Schemas</div>
            <div class="category-score">25/25</div>
            <div class="category-percentage">100.0%</div>
            <div class="progress-bar">
                <div class="progress-fill" style="width: 100.0%"></div>
            </div>
        </div>
                
        <div class="category-card warning">
            <div class="category-name">Error Handling</div>
            <div class="category-score">14/20</div>
            <div class="category-percentage">70.0%</div>
            <div class="progress-bar">
                <div class="progress-fill" style="width: 70.0%"></div>
            </div>
        </div>
                
        <div class="category-card success">
            <div class="category-name">Usability</div>
            <div class="category-score">18/20</div>
            <div class="category-percentage">90.0%</div>
            <div class="progress-bar">
                <div class="progress-fill" style="width: 90.0%"></div>
            </div>
        </div>
                
        <div class="category-card success">
            <div class="category-name">Authentication</div>
            <div class="category-score">9/10</div>
            <div class="category-percentage">90.0%</div>
            <div class="progress-bar">
                <div class="progress-fill" style="width: 90.0%"></div>
            </div>
        </div>
            </div>
        </div>

        <div class="metrics">
            <h3>Analysis Metrics</h3>
            <div class="metrics-grid">
                
        <div class="metric">
            <div class="metric-name">Operations with Descriptions</div>
            <div class="metric-value">3/3</div>
            <div class="metric-percentage">100.0%</div>
        </div>
                
        <div class="metric">
            <div class="metric-name">Parameters with Descriptions</div>
            <div class="metric-value">3/3</div>
            <div class="metric-percentage">100.0%</div>
        </div>
                
        <div class="metric">
            <div class="metric-name">Responses with Schemas</div>
            <div class="metric-value">9/9</div>
            <div class="metric-percentage">100.0%</div>
        </div>
                
        <div class="metric">
            <div class="metric-name">Operations with Examples</div>
            <div class="metric-value">3/3</div>
            <div class="metric-percentage">100.0%</div>
        </div>
            </div>
        </div>

        <div class="recommendations-summary"><h3>Key Recommendations</h3><div class="no-issues">✅ No critical or high priority issues found!</div></div>

        <footer class="footer">
            <p>Generated by API Quality Scorecard v1.0.0</p>
        </footer>
    </div>
</body>
</html>