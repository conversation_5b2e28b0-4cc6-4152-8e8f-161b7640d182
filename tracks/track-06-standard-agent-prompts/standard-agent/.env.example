# =============================================================================
# STANDARD AGENT - ENVIRONMENT CONFIGURATION
# =============================================================================
#
# QUICK SETUP:
# 1. Copy this file to `.env` in the same directory
# 2. Replace placeholder values with your actual API keys
# 3. At minimum, you need ONE LLM provider key to get started
# 4. Add JENTIC_AGENT_API_KEY for out-of-the-box tool access (recommended)
#
# =============================================================================

# -----------------------------------------------------------------------------
# LLM PROVIDER CONFIGURATION (REQUIRED)
# -----------------------------------------------------------------------------

# ANTHROPIC (Claude) - Recommended
# Get your key at: https://console.anthropic.com/
ANTHROPIC_API_KEY="your-anthropic-api-key-here"

# OPENAI (GPT)
# Get your key at: https://platform.openai.com/api-keys
OPENAI_API_KEY="your-openai-api-key-here"

# GOOGLE (Gemini)
# Get your key at: https://makersuite.google.com/app/apikey
GEMINI_API_KEY="your-google-gemini-api-key-here"

# Choose your preferred language model (ex: claude-sonnet-4-20250514, gpt-4o, gemini/gemini-2.0-flash-exp, vertex_ai/gemini-2.0-flash)

# NOTE for LiteLLM(out-of-the-box): If using the LiteLLM component, see https://docs.litellm.ai/docs/providers
# for the list of supported models and their exact names. Use the function-call-compatible model name.
LLM_MODEL="claude-sonnet-4-20250514"

# Optional sampling temperature (e.g., 0.0, 0.2, 1.0).
# If unset, we won’t send temperature.
# LLM_TEMPERATURE=0.2

# -----------------------------------------------------------------------------
# TOOL PROVIDER CONFIGURATION
# -----------------------------------------------------------------------------

# Jentic Platform (Out-of-the-box tool provider)
# Provides just in time tools for 1000+ APIs
# Get your agent apikey by visiting: https://app.jentic.com
JENTIC_AGENT_API_KEY ="your-jentic-api-key-here"

# Restrict tool search to only tools already configured and authorized in Jentic app.jentic.com
JENTIC_FILTER_BY_CREDENTIALS = True
