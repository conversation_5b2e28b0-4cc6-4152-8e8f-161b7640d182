<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jentic Summer Hackathon - Track Showcase</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .tracks-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .track-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .track-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 48px rgba(0,0,0,0.15);
        }

        .track-header {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            color: white;
            padding: 20px;
            position: relative;
        }

        .track-header h3 {
            font-size: 1.4rem;
            margin-bottom: 8px;
        }

        .track-header p {
            opacity: 0.9;
            font-size: 0.95rem;
        }

        .track-status {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(255,255,255,0.2);
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .track-content {
            padding: 25px;
        }

        .track-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .track-features {
            list-style: none;
            margin-bottom: 25px;
        }

        .track-features li {
            padding: 5px 0;
            color: #555;
        }

        .track-features li:before {
            content: "✓";
            color: #10b981;
            font-weight: bold;
            margin-right: 10px;
        }

        .run-button {
            width: 100%;
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .run-button:hover {
            background: linear-gradient(135deg, #059669, #047857);
            transform: translateY(-1px);
        }

        .run-button:disabled {
            background: #d1d5db;
            cursor: not-allowed;
            transform: none;
        }

        .output-section {
            margin-top: 20px;
            display: none;
        }

        .output-header {
            background: #1f2937;
            color: white;
            padding: 10px 15px;
            border-radius: 8px 8px 0 0;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .terminal-output {
            background: #111827;
            color: #10b981;
            padding: 20px;
            border-radius: 0 0 8px 8px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.85rem;
            line-height: 1.5;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .html-output {
            border: 1px solid #e5e7eb;
            border-radius: 0 0 8px 8px;
            max-height: 500px;
            overflow-y: auto;
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px;
            color: #6b7280;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #e5e7eb;
            border-top: 2px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            color: #ef4444;
            background: #fef2f2;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #ef4444;
        }

        .success {
            color: #10b981;
        }

        .footer {
            text-align: center;
            color: white;
            margin-top: 40px;
            opacity: 0.8;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .tracks-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-rocket"></i> Jentic Summer Hackathon</h1>
            <p>Interactive Track Showcase - Click to run and explore each implementation</p>
        </div>

        <div class="tracks-grid">
            <!-- Track 01: Standard Agent Discord Bot -->
            <div class="track-card">
                <div class="track-header">
                    <div class="track-status">✅ Complete</div>
                    <h3><i class="fab fa-discord"></i> Track 01 - Standard Agent Discord Bot</h3>
                    <p>AI-powered Discord bot with multi-step task execution</p>
                </div>
                <div class="track-content">
                    <div class="track-description">
                        A Discord bot powered by Standard Agent that can execute complex tasks using Jentic's tool ecosystem. Demonstrates autonomous reasoning and error recovery.
                    </div>
                    <ul class="track-features">
                        <li>Natural language task execution</li>
                        <li>Multi-API integration via Jentic</li>
                        <li>Discord slash commands</li>
                        <li>Error handling and recovery</li>
                    </ul>
                    <button class="run-button" onclick="runTrack('track01')">
                        <i class="fas fa-play"></i> Run Discord Bot Demo
                    </button>
                    <div class="output-section" id="output-track01">
                        <div class="output-header">
                            <i class="fas fa-terminal"></i> Discord Bot Output
                        </div>
                        <div class="terminal-output" id="terminal-track01"></div>
                    </div>
                </div>
            </div>

            <!-- Track 02: HAR to OpenAPI -->
            <div class="track-card">
                <div class="track-header">
                    <div class="track-status">✅ Complete</div>
                    <h3><i class="fas fa-exchange-alt"></i> Track 02 - HAR to OpenAPI</h3>
                    <p>Reverse engineer APIs from HTTP traffic</p>
                </div>
                <div class="track-content">
                    <div class="track-description">
                        Systematic approach to capture HTTP traffic and convert it to OpenAPI specifications. Expands the universe of APIs available to AI agents.
                    </div>
                    <ul class="track-features">
                        <li>HAR file analysis and sanitization</li>
                        <li>OpenAPI 3.0+ specification generation</li>
                        <li>Pattern recognition and extraction</li>
                        <li>Validation and testing workflows</li>
                    </ul>
                    <button class="run-button" onclick="runTrack('track02')">
                        <i class="fas fa-play"></i> Run HAR Analysis Demo
                    </button>
                    <div class="output-section" id="output-track02">
                        <div class="output-header">
                            <i class="fas fa-terminal"></i> HAR Analyzer Output
                        </div>
                        <div class="terminal-output" id="terminal-track02"></div>
                    </div>
                </div>
            </div>

            <!-- Track 05: OpenAPI Minifier -->
            <div class="track-card">
                <div class="track-header">
                    <div class="track-status">✅ Complete</div>
                    <h3><i class="fas fa-compress-alt"></i> Track 05 - OpenAPI Minifier</h3>
                    <p>Extract minimal API specs for specific use cases</p>
                </div>
                <div class="track-content">
                    <div class="track-description">
                        Smart minification tool that extracts only the operations and schemas needed for specific use cases, reducing massive API specs by 80-95%.
                    </div>
                    <ul class="track-features">
                        <li>Dependency graph analysis</li>
                        <li>Smart schema extraction</li>
                        <li>80-95% size reduction</li>
                        <li>Validation and correctness checks</li>
                    </ul>
                    <button class="run-button" onclick="runTrack('track05')">
                        <i class="fas fa-play"></i> Run Minifier Demo
                    </button>
                    <div class="output-section" id="output-track05">
                        <div class="output-header">
                            <i class="fas fa-terminal"></i> Minifier Output
                        </div>
                        <div class="terminal-output" id="terminal-track05"></div>
                    </div>
                </div>
            </div>

            <!-- Track 06: Standard Agent Prompts -->
            <div class="track-card">
                <div class="track-header">
                    <div class="track-status">✅ Complete</div>
                    <h3><i class="fas fa-comments"></i> Track 06 - Standard Agent Prompts</h3>
                    <p>High-quality prompts demonstrating agent capabilities</p>
                </div>
                <div class="track-content">
                    <div class="track-description">
                        Collection of verified, working prompts that demonstrate Standard Agent capabilities across different APIs and use cases.
                    </div>
                    <ul class="track-features">
                        <li>Multi-API workflow prompts</li>
                        <li>Error handling demonstrations</li>
                        <li>Performance benchmarking</li>
                        <li>Conversation flow examples</li>
                    </ul>
                    <button class="run-button" onclick="runTrack('track06')">
                        <i class="fas fa-play"></i> Run Prompt Collection Demo
                    </button>
                    <div class="output-section" id="output-track06">
                        <div class="output-header">
                            <i class="fas fa-terminal"></i> Prompt Testing Output
                        </div>
                        <div class="terminal-output" id="terminal-track06"></div>
                    </div>
                </div>
            </div>

            <!-- Track 07: API Quality Scorecard -->
            <div class="track-card">
                <div class="track-header">
                    <div class="track-status">✅ Complete</div>
                    <h3><i class="fas fa-chart-line"></i> Track 07 - API Quality Scorecard</h3>
                    <p>Evaluate OpenAPI specs for agent-readiness</p>
                </div>
                <div class="track-content">
                    <div class="track-description">
                        Automated scorecard system that evaluates OpenAPI specifications for agent-readiness, providing scores and actionable recommendations.
                    </div>
                    <ul class="track-features">
                        <li>5-category scoring framework (100 points)</li>
                        <li>HTML, JSON, Markdown reports</li>
                        <li>Actionable recommendations</li>
                        <li>Professional CLI interface</li>
                    </ul>
                    <button class="run-button" onclick="runTrack('track07')">
                        <i class="fas fa-play"></i> Run Quality Analysis
                    </button>
                    <div class="output-section" id="output-track07">
                        <div class="output-header">
                            <i class="fas fa-chart-bar"></i> Quality Scorecard Report
                        </div>
                        <div class="html-output" id="html-track07"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p><i class="fas fa-code"></i> Built with passion during Jentic Summer Hackathon 2024</p>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script>
        async function runTrack(trackId) {
            const button = document.querySelector(`button[onclick="runTrack('${trackId}')"]`);
            const outputSection = document.getElementById(`output-${trackId}`);
            const terminalOutput = document.getElementById(`terminal-${trackId}`);
            const htmlOutput = document.getElementById(`html-${trackId}`);
            
            // Show loading state
            button.disabled = true;
            button.innerHTML = '<div class="spinner"></div> Running...';
            outputSection.style.display = 'block';
            
            if (terminalOutput) {
                terminalOutput.innerHTML = '<div class="loading"><div class="spinner"></div>Executing...</div>';
            }
            if (htmlOutput) {
                htmlOutput.innerHTML = '<div class="loading"><div class="spinner"></div>Generating report...</div>';
            }

            try {
                const response = await fetch(`/run/${trackId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const result = await response.json();

                if (result.success) {
                    if (terminalOutput && result.output) {
                        terminalOutput.innerHTML = result.output;
                    }
                    if (htmlOutput && result.html) {
                        htmlOutput.innerHTML = result.html;
                    }
                } else {
                    const errorOutput = `<div class="error">Error: ${result.error}</div>`;
                    if (terminalOutput) {
                        terminalOutput.innerHTML = errorOutput;
                    }
                    if (htmlOutput) {
                        htmlOutput.innerHTML = errorOutput;
                    }
                }
            } catch (error) {
                const errorOutput = `<div class="error">Network Error: ${error.message}</div>`;
                if (terminalOutput) {
                    terminalOutput.innerHTML = errorOutput;
                }
                if (htmlOutput) {
                    htmlOutput.innerHTML = errorOutput;
                }
            } finally {
                // Reset button
                button.disabled = false;
                button.innerHTML = getButtonText(trackId);
            }
        }

        function getButtonText(trackId) {
            const buttonTexts = {
                'track01': '<i class="fas fa-play"></i> Run Discord Bot Demo',
                'track02': '<i class="fas fa-play"></i> Run HAR Analysis Demo',
                'track05': '<i class="fas fa-play"></i> Run Minifier Demo',
                'track06': '<i class="fas fa-play"></i> Run Prompt Collection Demo',
                'track07': '<i class="fas fa-play"></i> Run Quality Analysis'
            };
            return buttonTexts[trackId] || '<i class="fas fa-play"></i> Run Demo';
        }

        // Add some interactive animations
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.track-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>